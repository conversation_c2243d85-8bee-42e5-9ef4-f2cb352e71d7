import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { JwtPayload } from '../types';

// JWT secret'ı environment'tan al
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Authenticated request interface
export interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

/**
 * JWT token doğrulama middleware'i
 * Authorization header'ından token'ı alır ve doğrular
 */
export const authenticateToken = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Access token is required',
        statusCode: 401,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Token'ı doğrula
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    req.user = decoded;
    
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired token',
        statusCode: 401,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Token validation failed',
      statusCode: 500,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Role-based authorization middleware
 * Belirli roller için erişim kontrolü
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          error: 'Unauthorized',
          message: 'User not authenticated',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      if (!allowedRoles.includes(req.user.role)) {
        res.status(403).json({
          error: 'Forbidden',
          message: 'Insufficient permissions',
          statusCode: 403,
          timestamp: new Date().toISOString()
        });
        return;
      }

      next();
    } catch (error) {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Authorization check failed',
        statusCode: 500,
        timestamp: new Date().toISOString()
      });
    }
  };
};
