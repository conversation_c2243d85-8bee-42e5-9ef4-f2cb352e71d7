import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { AuthService } from '../services';
import { AuthenticatedRequest } from '../middlewares';

// Validation schemas
const loginSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  pin: z.string().min(4, 'PIN must be at least 4 characters').max(6, 'PIN must be at most 6 characters')
});

const employeeListSchema = z.object({
  branchId: z.string().optional()
});

export class AuthController {
  /**
   * Çalışan listesini getirir
   * GET /api/auth/employees
   */
  static async getEmployees(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Query parametrelerini validate et
      const { branchId } = employeeListSchema.parse(req.query);

      // Service'den çalışan listesini al
      const result = await AuthService.getEmployeeList(branchId);

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * PIN ile giriş yapar
   * POST /api/auth/login
   */
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Request body'yi validate et
      const { userId, pin } = loginSchema.parse(req.body);

      // Authentication service'ini çağır
      const result = await AuthService.authenticateWithPin(userId, pin);

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // Authentication hatalarını özel olarak handle et
      if (error instanceof Error) {
        const authErrors = [
          'User not found',
          'User account is inactive', 
          'PIN not set for this user',
          'Invalid PIN'
        ];

        if (authErrors.includes(error.message)) {
          res.status(401).json({
            success: false,
            error: 'Authentication Failed',
            message: error.message,
            statusCode: 401,
            timestamp: new Date().toISOString()
          });
          return;
        }
      }

      next(error);
    }
  }

  /**
   * Çıkış yapar (logout)
   * POST /api/auth/logout
   */
  static async logout(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user?.sessionId) {
        res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Session ID not found',
          statusCode: 400,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Session'ı sonlandır
      await AuthService.logout(req.user.sessionId);

      res.status(200).json({
        success: true,
        message: 'Logged out successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mevcut kullanıcı bilgilerini getirir
   * GET /api/auth/me
   */
  static async getCurrentUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Unauthorized',
          message: 'User not authenticated',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          user: req.user
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Token'ı yeniler
   * POST /api/auth/refresh
   */
  static async refreshToken(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Unauthorized',
          message: 'User not authenticated',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Mevcut session'ı validate et
      const authHeader = req.headers.authorization;
      const currentToken = authHeader && authHeader.split(' ')[1];

      if (!currentToken) {
        res.status(401).json({
          success: false,
          error: 'Unauthorized',
          message: 'Token not provided',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      const isValidSession = await AuthService.validateSession(currentToken);
      if (!isValidSession) {
        res.status(401).json({
          success: false,
          error: 'Unauthorized',
          message: 'Invalid session',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Yeni token ile giriş yap (aynı kullanıcı için)
      const result = await AuthService.authenticateWithPin(req.user.userId, ''); // PIN bypass for refresh

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}
