"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireRole = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// JWT secret'ı environment'tan al
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
/**
 * JWT token doğrulama middleware'i
 * Authorization header'ından token'ı alır ve doğrular
 */
const authenticateToken = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            res.status(401).json({
                error: 'Unauthorized',
                message: 'Access token is required',
                statusCode: 401,
                timestamp: new Date().toISOString()
            });
            return;
        }
        // Token'ı doğrula
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid or expired token',
                statusCode: 401,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Token validation failed',
            statusCode: 500,
            timestamp: new Date().toISOString()
        });
    }
};
exports.authenticateToken = authenticateToken;
/**
 * Role-based authorization middleware
 * Belirli roller için erişim kontrolü
 */
const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        try {
            if (!req.user) {
                res.status(401).json({
                    error: 'Unauthorized',
                    message: 'User not authenticated',
                    statusCode: 401,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            if (!allowedRoles.includes(req.user.role)) {
                res.status(403).json({
                    error: 'Forbidden',
                    message: 'Insufficient permissions',
                    statusCode: 403,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            next();
        }
        catch (error) {
            res.status(500).json({
                error: 'Internal Server Error',
                message: 'Authorization check failed',
                statusCode: 500,
                timestamp: new Date().toISOString()
            });
        }
    };
};
exports.requireRole = requireRole;
//# sourceMappingURL=auth.middleware.js.map