import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '../generated/client';
import { 
  EmployeeInfo, 
  EmployeeListResponse, 
  LoginResponse, 
  JwtPayload 
} from '../types';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '8h';

export class AuthService {
  /**
   * Çalışan listesini ve vardiya saatlerini getirir
   */
  static async getEmployeeList(branchId?: string): Promise<EmployeeListResponse> {
    try {
      // Çalışanları getir
      const users = await prisma.user.findMany({
        where: {
          active: true,
          ...(branchId && { branchId })
        },
        include: {
          branch: {
            select: {
              name: true,
              openingTime: true,
              closingTime: true
            }
          }
        },
        orderBy: [
          { role: 'asc' },
          { firstName: 'asc' }
        ]
      });

      // Çalışan bilgilerini formatla
      const employees: EmployeeInfo[] = users.map(user => ({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        branchId: user.branchId,
        branchName: user.branch?.name,
        active: user.active,
        hasPin: !!user.pin
      }));

      // Vardiya saatlerini al (ilk şubeden)
      const workingHours = users.length > 0 && users[0].branch ? {
        openingTime: users[0].branch.openingTime,
        closingTime: users[0].branch.closingTime
      } : undefined;

      return {
        employees,
        workingHours
      };
    } catch (error) {
      console.error('Error fetching employee list:', error);
      throw new Error('Failed to fetch employee list');
    }
  }

  /**
   * PIN ile kullanıcı doğrulama yapar
   */
  static async authenticateWithPin(userId: string, pin: string): Promise<LoginResponse> {
    try {
      // Kullanıcıyı bul
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          company: {
            select: { id: true, name: true }
          },
          branch: {
            select: { id: true, name: true }
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (!user.active) {
        throw new Error('User account is inactive');
      }

      if (!user.pin) {
        throw new Error('PIN not set for this user');
      }

      // PIN doğrulama
      const isPinValid = await bcrypt.compare(pin, user.pin);
      if (!isPinValid) {
        throw new Error('Invalid PIN');
      }

      // Session oluştur
      const session = await prisma.session.create({
        data: {
          userId: user.id,
          branchId: user.branchId || user.company.id, // Fallback to company if no branch
          token: '', // Token'ı aşağıda set edeceğiz
          deviceInfo: 'POS Terminal' // Bu bilgi frontend'den gelebilir
        }
      });

      // JWT payload oluştur
      const jwtPayload: JwtPayload = {
        userId: user.id,
        sessionId: session.id,
        branchId: user.branchId,
        companyId: user.companyId,
        role: user.role
      };

      // JWT token oluştur
      const token = jwt.sign(jwtPayload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
      } as jwt.SignOptions);

      // Session'ı token ile güncelle
      await prisma.session.update({
        where: { id: session.id },
        data: { token }
      });

      // Son giriş zamanını güncelle
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      });

      return {
        success: true,
        token,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          branchId: user.branchId,
          companyId: user.companyId
        },
        session: {
          id: session.id,
          startedAt: session.startedAt.toISOString()
        }
      };
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  }

  /**
   * Session'ı sonlandırır (logout)
   */
  static async logout(sessionId: string): Promise<void> {
    try {
      await prisma.session.update({
        where: { id: sessionId },
        data: { endedAt: new Date() }
      });
    } catch (error) {
      console.error('Logout error:', error);
      throw new Error('Failed to logout');
    }
  }

  /**
   * Token'dan session bilgisini doğrular
   */
  static async validateSession(token: string): Promise<boolean> {
    try {
      const session = await prisma.session.findUnique({
        where: { 
          token,
          endedAt: null // Aktif session
        }
      });

      return !!session;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  }
}
