import { UserRole } from '../generated/client';

// ==================== REQUEST TYPES ====================

export interface LoginRequest {
  userId: string;
  pin: string;
}

export interface EmployeeListRequest {
  branchId?: string;
}

// ==================== RESPONSE TYPES ====================

export interface EmployeeInfo {
  id: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  branchId: string | null;
  branchName?: string;
  active: boolean;
  hasPin: boolean;
}

export interface EmployeeListResponse {
  employees: EmployeeInfo[];
  workingHours?: {
    openingTime: string | null;
    closingTime: string | null;
  };
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    branchId: string | null;
    companyId: string;
  };
  session: {
    id: string;
    startedAt: string;
  };
}

// ==================== JWT PAYLOAD ====================

export interface JwtPayload {
  userId: string;
  sessionId: string;
  branchId: string | null;
  companyId: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

// ==================== ERROR TYPES ====================

export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

export interface ValidationError extends ApiError {
  details: Array<{
    field: string;
    message: string;
  }>;
}

// ==================== MIDDLEWARE TYPES ====================

export interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}
