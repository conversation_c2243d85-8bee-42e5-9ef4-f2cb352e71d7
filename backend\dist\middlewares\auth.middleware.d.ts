import { Request, Response, NextFunction } from 'express';
import { JwtPayload } from '../types';
export interface AuthenticatedRequest extends Request {
    user?: JwtPayload;
}
/**
 * JWT token doğrulama middleware'i
 * Authorization header'ından token'ı alır ve doğrular
 */
export declare const authenticateToken: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
/**
 * Role-based authorization middleware
 * Belirli roller için erişim kont<PERSON>
 */
export declare const requireRole: (allowedRoles: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.middleware.d.ts.map