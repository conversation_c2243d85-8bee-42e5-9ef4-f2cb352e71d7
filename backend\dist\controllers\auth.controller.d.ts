import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../middlewares';
export declare class AuthController {
    /**
     * <PERSON>alışan listesini getirir
     * GET /api/auth/employees
     */
    static getEmployees(req: Request, res: Response, next: NextFunction): Promise<void>;
    /**
     * PIN ile giriş yapar
     * POST /api/auth/login
     */
    static login(req: Request, res: Response, next: NextFunction): Promise<void>;
    /**
     * Çıkış yapar (logout)
     * POST /api/auth/logout
     */
    static logout(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    /**
     * Mevcut kullanıcı bilgilerini getirir
     * GET /api/auth/me
     */
    static getCurrentUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    /**
     * Token'ı yeniler
     * POST /api/auth/refresh
     */
    static refreshToken(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=auth.controller.d.ts.map