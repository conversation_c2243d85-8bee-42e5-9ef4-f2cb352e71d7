import { Router } from 'express';
import { AuthController } from '../controllers';
import { authenticateToken } from '../middlewares';

const router = Router();

// ==================== PUBLIC ROUTES ====================

/**
 * Çalışan listesini getirir
 * GET /api/auth/employees?branchId=optional
 */
router.get('/employees', AuthController.getEmployees);

/**
 * PIN ile giriş yapar
 * POST /api/auth/login
 * Body: { userId: string, pin: string }
 */
router.post('/login', AuthController.login);

// ==================== PROTECTED ROUTES ====================

/**
 * Çıkış yapar (logout)
 * POST /api/auth/logout
 * Headers: Authorization: Bearer <token>
 */
router.post('/logout', authenticateToken, AuthController.logout);

/**
 * Mevcut kullanıcı bilgilerini getirir
 * GET /api/auth/me
 * Headers: Authorization: Bearer <token>
 */
router.get('/me', authenticateToken, AuthController.getCurrentUser);

/**
 * Token'ı yeniler
 * POST /api/auth/refresh
 * Headers: Authorization: Bearer <token>
 */
router.post('/refresh', authenticateToken, AuthController.refreshToken);

export default router;
