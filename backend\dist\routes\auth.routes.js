"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const middlewares_1 = require("../middlewares");
const router = (0, express_1.Router)();
// ==================== PUBLIC ROUTES ====================
/**
 * Çalışan listesini getirir
 * GET /api/auth/employees?branchId=optional
 */
router.get('/employees', controllers_1.AuthController.getEmployees);
/**
 * PIN ile giriş yapar
 * POST /api/auth/login
 * Body: { userId: string, pin: string }
 */
router.post('/login', controllers_1.AuthController.login);
// ==================== PROTECTED ROUTES ====================
/**
 * Çıkış yapar (logout)
 * POST /api/auth/logout
 * Headers: Authorization: Bearer <token>
 */
router.post('/logout', middlewares_1.authenticateToken, controllers_1.AuthController.logout);
/**
 * Mevcut kullanıcı bilgilerini getirir
 * GET /api/auth/me
 * Headers: Authorization: Bearer <token>
 */
router.get('/me', middlewares_1.authenticateToken, controllers_1.AuthController.getCurrentUser);
/**
 * Token'ı yeniler
 * POST /api/auth/refresh
 * Headers: Authorization: Bearer <token>
 */
router.post('/refresh', middlewares_1.authenticateToken, controllers_1.AuthController.refreshToken);
exports.default = router;
//# sourceMappingURL=auth.routes.js.map