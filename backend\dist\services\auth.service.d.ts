import { EmployeeListResponse, LoginResponse } from '../types';
export declare class AuthService {
    /**
     * <PERSON><PERSON><PERSON>şan listesini ve vardiya saatlerini getirir
     */
    static getEmployeeList(branchId?: string): Promise<EmployeeListResponse>;
    /**
     * PIN ile kullanıcı doğrulama yapar
     */
    static authenticateWithPin(userId: string, pin: string): Promise<LoginResponse>;
    /**
     * Session'ı sonlandırır (logout)
     */
    static logout(sessionId: string): Promise<void>;
    /**
     * Token'dan session bilgisini doğrular
     */
    static validateSession(token: string): Promise<boolean>;
}
//# sourceMappingURL=auth.service.d.ts.map