-- Test verisi oluşturma script'i
-- Bu script'i PostgreSQL'de çalıştırarak test verisi oluşturabilirsiniz

-- Company oluştur
INSERT INTO "Company" (id, name, "taxNumber", "taxOffice", address, phone, email, "createdAt", "updatedAt")
VALUES ('comp1', 'Test Restaurant', '1234567890', 'Kadıköy VD', 'İstanbul', '02121234567', '<EMAIL>', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Branch oluştur
INSERT INTO "Branch" (id, "companyId", code, name, address, phone, "openingTime", "closingTime", "createdAt", "updatedAt")
VALUES ('branch1', 'comp1', 'IST01', 'Ana Şube', 'Kadıköy, İstanbul', '02121234567', '09:00', '23:00', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Test k<PERSON> oluştur (PIN'ler bcrypt ile hash'lenmiş)
-- PIN: 1234 -> $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi
-- PIN: 5678 -> $2a$10$8K1p/a0dhrxiH8Tf2nd2tOzIl9/P2iDXFaf6wmP/.akxOvl9yqfou

INSERT INTO "User" (id, "companyId", "branchId", username, password, pin, "firstName", "lastName", role, active, "createdAt", "updatedAt")
VALUES 
  ('user1', 'comp1', 'branch1', 'ahmet.yilmaz', 'hashed_password', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ahmet', 'Yılmaz', 'CASHIER', true, NOW(), NOW()),
  ('user2', 'comp1', 'branch1', 'ayse.demir', 'hashed_password', '$2a$10$8K1p/a0dhrxiH8Tf2nd2tOzIl9/P2iDXFaf6wmP/.akxOvl9yqfou', 'Ayşe', 'Demir', 'WAITER', true, NOW(), NOW()),
  ('user3', 'comp1', 'branch1', 'mehmet.kaya', 'hashed_password', NULL, 'Mehmet', 'Kaya', 'KITCHEN', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;
