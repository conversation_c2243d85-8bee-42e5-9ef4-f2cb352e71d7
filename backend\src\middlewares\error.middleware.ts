import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { ApiError, ValidationError } from '../types';

/**
 * Global error handling middleware
 * Tüm hataları yakalar ve uygun HTTP response döner
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Zod validation errors
  if (error instanceof ZodError) {
    const validationError: ValidationError = {
      error: 'Validation Error',
      message: 'Request validation failed',
      statusCode: 400,
      timestamp: new Date().toISOString(),
      details: error.issues.map((err: any) => ({
        field: err.path.join('.'),
        message: err.message
      }))
    };

    res.status(400).json(validationError);
    return;
  }

  // Custom API errors
  if (error.statusCode) {
    const apiError: ApiError = {
      error: error.name || 'API Error',
      message: error.message,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    };

    res.status(error.statusCode).json(apiError);
    return;
  }

  // Database errors
  if (error.code === 'P2002') { // Prisma unique constraint
    res.status(409).json({
      error: 'Conflict',
      message: 'Resource already exists',
      statusCode: 409,
      timestamp: new Date().toISOString()
    });
    return;
  }

  if (error.code === 'P2025') { // Prisma record not found
    res.status(404).json({
      error: 'Not Found',
      message: 'Resource not found',
      statusCode: 404,
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Default server error
  const serverError: ApiError = {
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' 
      ? error.message 
      : 'Something went wrong',
    statusCode: 500,
    timestamp: new Date().toISOString()
  };

  res.status(500).json(serverError);
};

/**
 * 404 handler for undefined routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`,
    statusCode: 404,
    timestamp: new Date().toISOString()
  });
};
