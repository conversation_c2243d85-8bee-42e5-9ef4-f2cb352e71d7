# API Test Script
Write-Host "🧪 Testing Atorpos POS Authentication API" -ForegroundColor Green

# 1. Test Employee List
Write-Host "`n1️⃣ Testing Employee List..." -ForegroundColor Yellow
try {
    $employees = Invoke-RestMethod -Uri "http://localhost:3001/api/auth/employees" -Method GET
    Write-Host "✅ Employee List Success" -ForegroundColor Green
    Write-Host "   Found $($employees.data.employees.Count) employees"
    Write-Host "   Working Hours: $($employees.data.workingHours.openingTime) - $($employees.data.workingHours.closingTime)"
} catch {
    Write-Host "❌ Employee List Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Test Successful Login
Write-Host "`n2️⃣ Testing Successful Login..." -ForegroundColor Yellow
try {
    $loginBody = @{
        userId = "user1"
        pin = "1234"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.data.token
    
    Write-Host "✅ Login Success" -ForegroundColor Green
    Write-Host "   User: $($loginResponse.data.user.firstName) $($loginResponse.data.user.lastName)"
    Write-Host "   Role: $($loginResponse.data.user.role)"
    Write-Host "   Token: $($token.Substring(0,20))..."
} catch {
    Write-Host "❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test Invalid PIN
Write-Host "`n3️⃣ Testing Invalid PIN..." -ForegroundColor Yellow
try {
    $invalidBody = @{
        userId = "user1"
        pin = "9999"
    } | ConvertTo-Json
    
    Invoke-RestMethod -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $invalidBody -ContentType "application/json"
    Write-Host "❌ Should have failed but didn't" -ForegroundColor Red
} catch {
    Write-Host "✅ Invalid PIN correctly rejected" -ForegroundColor Green
    Write-Host "   Status: $($_.Exception.Response.StatusCode)"
}

# 4. Test Validation Error
Write-Host "`n4️⃣ Testing Validation Error..." -ForegroundColor Yellow
try {
    $invalidBody = @{
        userId = ""
        pin = "12"
    } | ConvertTo-Json
    
    Invoke-RestMethod -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $invalidBody -ContentType "application/json"
    Write-Host "❌ Should have failed but didn't" -ForegroundColor Red
} catch {
    Write-Host "✅ Validation error correctly handled" -ForegroundColor Green
    Write-Host "   Status: $($_.Exception.Response.StatusCode)"
}

# 5. Test Protected Endpoint with Token
if ($token) {
    Write-Host "`n5️⃣ Testing Protected Endpoint..." -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
        }
        
        $meResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/auth/me" -Method GET -Headers $headers
        Write-Host "✅ Protected endpoint access success" -ForegroundColor Green
        Write-Host "   User ID: $($meResponse.data.user.userId)"
        Write-Host "   Role: $($meResponse.data.user.role)"
    } catch {
        Write-Host "❌ Protected endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 6. Test Protected Endpoint without Token
Write-Host "`n6️⃣ Testing Protected Endpoint without Token..." -ForegroundColor Yellow
try {
    Invoke-RestMethod -Uri "http://localhost:3001/api/auth/me" -Method GET
    Write-Host "❌ Should have failed but didn't" -ForegroundColor Red
} catch {
    Write-Host "✅ Unauthorized access correctly rejected" -ForegroundColor Green
    Write-Host "   Status: $($_.Exception.Response.StatusCode)"
}

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Green
