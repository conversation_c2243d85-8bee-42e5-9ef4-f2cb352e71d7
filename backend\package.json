{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon src/index.ts", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "prisma": "^6.11.1", "zod": "^4.0.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/node": "^24.0.13", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}