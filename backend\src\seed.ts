import bcrypt from 'bcryptjs';
import { PrismaClient } from './generated/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Company oluştur
  const company = await prisma.company.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      id: 'comp1',
      name: 'Test Restaurant',
      taxNumber: '1234567890',
      taxOffice: 'Kadıköy VD',
      address: 'İstanbul',
      phone: '02121234567',
      email: '<EMAIL>'
    }
  });

  console.log('✅ Company created:', company.name);

  // Branch oluştur
  const branch = await prisma.branch.upsert({
    where: { id: 'branch1' },
    update: {},
    create: {
      id: 'branch1',
      companyId: company.id,
      code: 'IST01',
      name: '<PERSON> Şube',
      address: 'Kadıköy, İstanbul',
      phone: '02121234567',
      openingTime: '09:00',
      closingTime: '23:00'
    }
  });

  console.log('✅ Branch created:', branch.name);

  // PIN'leri hash'le
  const pin1234 = await bcrypt.hash('1234', 10);
  const pin5678 = await bcrypt.hash('5678', 10);
  const password = await bcrypt.hash('password123', 10);

  // Test kullanıcıları oluştur
  const users = [
    {
      id: 'user1',
      username: 'ahmet.yilmaz',
      password,
      pin: pin1234,
      firstName: 'Ahmet',
      lastName: 'Yılmaz',
      role: 'CASHIER' as const
    },
    {
      id: 'user2',
      username: 'ayse.demir',
      password,
      pin: pin5678,
      firstName: 'Ayşe',
      lastName: 'Demir',
      role: 'WAITER' as const
    },
    {
      id: 'user3',
      username: 'mehmet.kaya',
      password,
      pin: null,
      firstName: 'Mehmet',
      lastName: 'Kaya',
      role: 'KITCHEN' as const
    }
  ];

  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { id: userData.id },
      update: {},
      create: {
        ...userData,
        companyId: company.id,
        branchId: branch.id
      }
    });

    console.log(`✅ User created: ${user.firstName} ${user.lastName} (${user.role})`);
  }

  console.log('🎉 Seeding completed!');
  console.log('\n📋 Test Data:');
  console.log('- Ahmet Yılmaz (CASHIER) - PIN: 1234');
  console.log('- Ayşe Demir (WAITER) - PIN: 5678');
  console.log('- Mehmet Kaya (KITCHEN) - No PIN');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
