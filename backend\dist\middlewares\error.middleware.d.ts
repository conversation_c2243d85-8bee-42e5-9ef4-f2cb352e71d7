import { Request, Response, NextFunction } from 'express';
/**
 * Global error handling middleware
 * Tüm hataları yakalar ve uygun HTTP response döner
 */
export declare const errorHandler: (error: any, req: Request, res: Response, next: NextFunction) => void;
/**
 * 404 handler for undefined routes
 */
export declare const notFoundHandler: (req: Request, res: Response) => void;
//# sourceMappingURL=error.middleware.d.ts.map